"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/i18next-browser-languagedetector";
exports.ids = ["vendor-chunks/i18next-browser-languagedetector"];
exports.modules = {

/***/ "(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Browser)\n/* harmony export */ });\nconst {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, value, cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name, domain) {\n    this.create(name, '', -1, domain);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hash = {\n  name: 'hash',\n  // Deconstruct the options object and extract the lookupHash property and the lookupFromHashIndex property\n  lookup(_ref) {\n    let {\n      lookupHash,\n      lookupFromHashIndex\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      const {\n        hash\n      } = window.location;\n      if (hash && hash.length > 2) {\n        const query = hash.substring(1);\n        if (lookupHash) {\n          const params = query.split('&');\n          for (let i = 0; i < params.length; i++) {\n            const pos = params[i].indexOf('=');\n            if (pos > 0) {\n              const key = params[i].substring(0, pos);\n              if (key === lookupHash) {\n                found = params[i].substring(pos + 1);\n              }\n            }\n          }\n        }\n        if (found) return found;\n        if (!found && lookupFromHashIndex > -1) {\n          const language = hash.match(/\\/([a-zA-Z-]*)/g);\n          if (!Array.isArray(language)) return undefined;\n          const index = typeof lookupFromHashIndex === 'number' ? lookupFromHashIndex : 0;\n          return language[index]?.replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n    this.addDetector(hash);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaTE4bmV4dC1icm93c2VyLWxhbmd1YWdlZGV0ZWN0b3IvZGlzdC9lc20vaTE4bmV4dEJyb3dzZXJMYW5ndWFnZURldGVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsS0FBSyxHQUFHLE1BQU07QUFDN0I7QUFDQTtBQUNBO0FBQ0EsY0FBYyxVQUFVLG1CQUFtQjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxTQUFTLFdBQVc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsT0FBTyxTQUFTO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFVBQVUsMEJBQTBCO0FBQ2xEO0FBQ0EsOEJBQThCO0FBQzlCLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxzQkFBc0IsS0FBSztBQUMzQix1Q0FBdUM7QUFDdkMsb0JBQW9CLGVBQWU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLG1CQUFtQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsbUJBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsMkVBQTJFO0FBQzNFO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSx3QkFBd0I7QUFDeEIsd0JBQXdCLHNCQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkZBQTZGLElBQUksZUFBZSxLQUFLLFFBQVEsSUFBSTs7QUFFakk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSw0SEFBNEg7QUFDNUgscURBQXFEO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9ub2RlX21vZHVsZXMvaTE4bmV4dC1icm93c2VyLWxhbmd1YWdlZGV0ZWN0b3IvZGlzdC9lc20vaTE4bmV4dEJyb3dzZXJMYW5ndWFnZURldGVjdG9yLmpzP2JkNzQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qge1xuICBzbGljZSxcbiAgZm9yRWFjaFxufSA9IFtdO1xuZnVuY3Rpb24gZGVmYXVsdHMob2JqKSB7XG4gIGZvckVhY2guY2FsbChzbGljZS5jYWxsKGFyZ3VtZW50cywgMSksIHNvdXJjZSA9PiB7XG4gICAgaWYgKHNvdXJjZSkge1xuICAgICAgZm9yIChjb25zdCBwcm9wIGluIHNvdXJjZSkge1xuICAgICAgICBpZiAob2JqW3Byb3BdID09PSB1bmRlZmluZWQpIG9ialtwcm9wXSA9IHNvdXJjZVtwcm9wXTtcbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICByZXR1cm4gb2JqO1xufVxuZnVuY3Rpb24gaGFzWFNTKGlucHV0KSB7XG4gIGlmICh0eXBlb2YgaW5wdXQgIT09ICdzdHJpbmcnKSByZXR1cm4gZmFsc2U7XG5cbiAgLy8gQ29tbW9uIFhTUyBhdHRhY2sgcGF0dGVybnNcbiAgY29uc3QgeHNzUGF0dGVybnMgPSBbLzxcXHMqc2NyaXB0Lio/Pi9pLCAvPFxccypcXC9cXHMqc2NyaXB0XFxzKj4vaSwgLzxcXHMqaW1nLio/b25cXHcrXFxzKj0vaSwgLzxcXHMqXFx3K1xccypvblxcdytcXHMqPS4qPz4vaSwgL2phdmFzY3JpcHRcXHMqOi9pLCAvdmJzY3JpcHRcXHMqOi9pLCAvZXhwcmVzc2lvblxccypcXCgvaSwgL2V2YWxcXHMqXFwoL2ksIC9hbGVydFxccypcXCgvaSwgL2RvY3VtZW50XFwuY29va2llL2ksIC9kb2N1bWVudFxcLndyaXRlXFxzKlxcKC9pLCAvd2luZG93XFwubG9jYXRpb24vaSwgL2lubmVySFRNTC9pXTtcbiAgcmV0dXJuIHhzc1BhdHRlcm5zLnNvbWUocGF0dGVybiA9PiBwYXR0ZXJuLnRlc3QoaW5wdXQpKTtcbn1cblxuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnRyb2wtcmVnZXhcbmNvbnN0IGZpZWxkQ29udGVudFJlZ0V4cCA9IC9eW1xcdTAwMDlcXHUwMDIwLVxcdTAwN2VcXHUwMDgwLVxcdTAwZmZdKyQvO1xuY29uc3Qgc2VyaWFsaXplQ29va2llID0gZnVuY3Rpb24gKG5hbWUsIHZhbCkge1xuICBsZXQgb3B0aW9ucyA9IGFyZ3VtZW50cy5sZW5ndGggPiAyICYmIGFyZ3VtZW50c1syXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzJdIDoge1xuICAgIHBhdGg6ICcvJ1xuICB9O1xuICBjb25zdCBvcHQgPSBvcHRpb25zO1xuICBjb25zdCB2YWx1ZSA9IGVuY29kZVVSSUNvbXBvbmVudCh2YWwpO1xuICBsZXQgc3RyID0gYCR7bmFtZX09JHt2YWx1ZX1gO1xuICBpZiAob3B0Lm1heEFnZSA+IDApIHtcbiAgICBjb25zdCBtYXhBZ2UgPSBvcHQubWF4QWdlIC0gMDtcbiAgICBpZiAoTnVtYmVyLmlzTmFOKG1heEFnZSkpIHRocm93IG5ldyBFcnJvcignbWF4QWdlIHNob3VsZCBiZSBhIE51bWJlcicpO1xuICAgIHN0ciArPSBgOyBNYXgtQWdlPSR7TWF0aC5mbG9vcihtYXhBZ2UpfWA7XG4gIH1cbiAgaWYgKG9wdC5kb21haW4pIHtcbiAgICBpZiAoIWZpZWxkQ29udGVudFJlZ0V4cC50ZXN0KG9wdC5kb21haW4pKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdvcHRpb24gZG9tYWluIGlzIGludmFsaWQnKTtcbiAgICB9XG4gICAgc3RyICs9IGA7IERvbWFpbj0ke29wdC5kb21haW59YDtcbiAgfVxuICBpZiAob3B0LnBhdGgpIHtcbiAgICBpZiAoIWZpZWxkQ29udGVudFJlZ0V4cC50ZXN0KG9wdC5wYXRoKSkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIHBhdGggaXMgaW52YWxpZCcpO1xuICAgIH1cbiAgICBzdHIgKz0gYDsgUGF0aD0ke29wdC5wYXRofWA7XG4gIH1cbiAgaWYgKG9wdC5leHBpcmVzKSB7XG4gICAgaWYgKHR5cGVvZiBvcHQuZXhwaXJlcy50b1VUQ1N0cmluZyAhPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIGV4cGlyZXMgaXMgaW52YWxpZCcpO1xuICAgIH1cbiAgICBzdHIgKz0gYDsgRXhwaXJlcz0ke29wdC5leHBpcmVzLnRvVVRDU3RyaW5nKCl9YDtcbiAgfVxuICBpZiAob3B0Lmh0dHBPbmx5KSBzdHIgKz0gJzsgSHR0cE9ubHknO1xuICBpZiAob3B0LnNlY3VyZSkgc3RyICs9ICc7IFNlY3VyZSc7XG4gIGlmIChvcHQuc2FtZVNpdGUpIHtcbiAgICBjb25zdCBzYW1lU2l0ZSA9IHR5cGVvZiBvcHQuc2FtZVNpdGUgPT09ICdzdHJpbmcnID8gb3B0LnNhbWVTaXRlLnRvTG93ZXJDYXNlKCkgOiBvcHQuc2FtZVNpdGU7XG4gICAgc3dpdGNoIChzYW1lU2l0ZSkge1xuICAgICAgY2FzZSB0cnVlOlxuICAgICAgICBzdHIgKz0gJzsgU2FtZVNpdGU9U3RyaWN0JztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdsYXgnOlxuICAgICAgICBzdHIgKz0gJzsgU2FtZVNpdGU9TGF4JztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdzdHJpY3QnOlxuICAgICAgICBzdHIgKz0gJzsgU2FtZVNpdGU9U3RyaWN0JztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdub25lJzpcbiAgICAgICAgc3RyICs9ICc7IFNhbWVTaXRlPU5vbmUnO1xuICAgICAgICBicmVhaztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ29wdGlvbiBzYW1lU2l0ZSBpcyBpbnZhbGlkJyk7XG4gICAgfVxuICB9XG4gIGlmIChvcHQucGFydGl0aW9uZWQpIHN0ciArPSAnOyBQYXJ0aXRpb25lZCc7XG4gIHJldHVybiBzdHI7XG59O1xuY29uc3QgY29va2llID0ge1xuICBjcmVhdGUobmFtZSwgdmFsdWUsIG1pbnV0ZXMsIGRvbWFpbikge1xuICAgIGxldCBjb29raWVPcHRpb25zID0gYXJndW1lbnRzLmxlbmd0aCA+IDQgJiYgYXJndW1lbnRzWzRdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbNF0gOiB7XG4gICAgICBwYXRoOiAnLycsXG4gICAgICBzYW1lU2l0ZTogJ3N0cmljdCdcbiAgICB9O1xuICAgIGlmIChtaW51dGVzKSB7XG4gICAgICBjb29raWVPcHRpb25zLmV4cGlyZXMgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29va2llT3B0aW9ucy5leHBpcmVzLnNldFRpbWUoY29va2llT3B0aW9ucy5leHBpcmVzLmdldFRpbWUoKSArIG1pbnV0ZXMgKiA2MCAqIDEwMDApO1xuICAgIH1cbiAgICBpZiAoZG9tYWluKSBjb29raWVPcHRpb25zLmRvbWFpbiA9IGRvbWFpbjtcbiAgICBkb2N1bWVudC5jb29raWUgPSBzZXJpYWxpemVDb29raWUobmFtZSwgdmFsdWUsIGNvb2tpZU9wdGlvbnMpO1xuICB9LFxuICByZWFkKG5hbWUpIHtcbiAgICBjb25zdCBuYW1lRVEgPSBgJHtuYW1lfT1gO1xuICAgIGNvbnN0IGNhID0gZG9jdW1lbnQuY29va2llLnNwbGl0KCc7Jyk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjYS5sZW5ndGg7IGkrKykge1xuICAgICAgbGV0IGMgPSBjYVtpXTtcbiAgICAgIHdoaWxlIChjLmNoYXJBdCgwKSA9PT0gJyAnKSBjID0gYy5zdWJzdHJpbmcoMSwgYy5sZW5ndGgpO1xuICAgICAgaWYgKGMuaW5kZXhPZihuYW1lRVEpID09PSAwKSByZXR1cm4gYy5zdWJzdHJpbmcobmFtZUVRLmxlbmd0aCwgYy5sZW5ndGgpO1xuICAgIH1cbiAgICByZXR1cm4gbnVsbDtcbiAgfSxcbiAgcmVtb3ZlKG5hbWUsIGRvbWFpbikge1xuICAgIHRoaXMuY3JlYXRlKG5hbWUsICcnLCAtMSwgZG9tYWluKTtcbiAgfVxufTtcbnZhciBjb29raWUkMSA9IHtcbiAgbmFtZTogJ2Nvb2tpZScsXG4gIC8vIERlY29uc3RydWN0IHRoZSBvcHRpb25zIG9iamVjdCBhbmQgZXh0cmFjdCB0aGUgbG9va3VwQ29va2llIHByb3BlcnR5XG4gIGxvb2t1cChfcmVmKSB7XG4gICAgbGV0IHtcbiAgICAgIGxvb2t1cENvb2tpZVxuICAgIH0gPSBfcmVmO1xuICAgIGlmIChsb29rdXBDb29raWUgJiYgdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgcmV0dXJuIGNvb2tpZS5yZWFkKGxvb2t1cENvb2tpZSkgfHwgdW5kZWZpbmVkO1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9LFxuICAvLyBEZWNvbnN0cnVjdCB0aGUgb3B0aW9ucyBvYmplY3QgYW5kIGV4dHJhY3QgdGhlIGxvb2t1cENvb2tpZSwgY29va2llTWludXRlcywgY29va2llRG9tYWluLCBhbmQgY29va2llT3B0aW9ucyBwcm9wZXJ0aWVzXG4gIGNhY2hlVXNlckxhbmd1YWdlKGxuZywgX3JlZjIpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwQ29va2llLFxuICAgICAgY29va2llTWludXRlcyxcbiAgICAgIGNvb2tpZURvbWFpbixcbiAgICAgIGNvb2tpZU9wdGlvbnNcbiAgICB9ID0gX3JlZjI7XG4gICAgaWYgKGxvb2t1cENvb2tpZSAmJiB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb29raWUuY3JlYXRlKGxvb2t1cENvb2tpZSwgbG5nLCBjb29raWVNaW51dGVzLCBjb29raWVEb21haW4sIGNvb2tpZU9wdGlvbnMpO1xuICAgIH1cbiAgfVxufTtcblxudmFyIHF1ZXJ5c3RyaW5nID0ge1xuICBuYW1lOiAncXVlcnlzdHJpbmcnLFxuICAvLyBEZWNvbnN0cnVjdCB0aGUgb3B0aW9ucyBvYmplY3QgYW5kIGV4dHJhY3QgdGhlIGxvb2t1cFF1ZXJ5c3RyaW5nIHByb3BlcnR5XG4gIGxvb2t1cChfcmVmKSB7XG4gICAgbGV0IHtcbiAgICAgIGxvb2t1cFF1ZXJ5c3RyaW5nXG4gICAgfSA9IF9yZWY7XG4gICAgbGV0IGZvdW5kO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbGV0IHtcbiAgICAgICAgc2VhcmNoXG4gICAgICB9ID0gd2luZG93LmxvY2F0aW9uO1xuICAgICAgaWYgKCF3aW5kb3cubG9jYXRpb24uc2VhcmNoICYmIHdpbmRvdy5sb2NhdGlvbi5oYXNoPy5pbmRleE9mKCc/JykgPiAtMSkge1xuICAgICAgICBzZWFyY2ggPSB3aW5kb3cubG9jYXRpb24uaGFzaC5zdWJzdHJpbmcod2luZG93LmxvY2F0aW9uLmhhc2guaW5kZXhPZignPycpKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHF1ZXJ5ID0gc2VhcmNoLnN1YnN0cmluZygxKTtcbiAgICAgIGNvbnN0IHBhcmFtcyA9IHF1ZXJ5LnNwbGl0KCcmJyk7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhcmFtcy5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBwb3MgPSBwYXJhbXNbaV0uaW5kZXhPZignPScpO1xuICAgICAgICBpZiAocG9zID4gMCkge1xuICAgICAgICAgIGNvbnN0IGtleSA9IHBhcmFtc1tpXS5zdWJzdHJpbmcoMCwgcG9zKTtcbiAgICAgICAgICBpZiAoa2V5ID09PSBsb29rdXBRdWVyeXN0cmluZykge1xuICAgICAgICAgICAgZm91bmQgPSBwYXJhbXNbaV0uc3Vic3RyaW5nKHBvcyArIDEpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZm91bmQ7XG4gIH1cbn07XG5cbnZhciBoYXNoID0ge1xuICBuYW1lOiAnaGFzaCcsXG4gIC8vIERlY29uc3RydWN0IHRoZSBvcHRpb25zIG9iamVjdCBhbmQgZXh0cmFjdCB0aGUgbG9va3VwSGFzaCBwcm9wZXJ0eSBhbmQgdGhlIGxvb2t1cEZyb21IYXNoSW5kZXggcHJvcGVydHlcbiAgbG9va3VwKF9yZWYpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwSGFzaCxcbiAgICAgIGxvb2t1cEZyb21IYXNoSW5kZXhcbiAgICB9ID0gX3JlZjtcbiAgICBsZXQgZm91bmQ7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIGhhc2hcbiAgICAgIH0gPSB3aW5kb3cubG9jYXRpb247XG4gICAgICBpZiAoaGFzaCAmJiBoYXNoLmxlbmd0aCA+IDIpIHtcbiAgICAgICAgY29uc3QgcXVlcnkgPSBoYXNoLnN1YnN0cmluZygxKTtcbiAgICAgICAgaWYgKGxvb2t1cEhhc2gpIHtcbiAgICAgICAgICBjb25zdCBwYXJhbXMgPSBxdWVyeS5zcGxpdCgnJicpO1xuICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGFyYW1zLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBwb3MgPSBwYXJhbXNbaV0uaW5kZXhPZignPScpO1xuICAgICAgICAgICAgaWYgKHBvcyA+IDApIHtcbiAgICAgICAgICAgICAgY29uc3Qga2V5ID0gcGFyYW1zW2ldLnN1YnN0cmluZygwLCBwb3MpO1xuICAgICAgICAgICAgICBpZiAoa2V5ID09PSBsb29rdXBIYXNoKSB7XG4gICAgICAgICAgICAgICAgZm91bmQgPSBwYXJhbXNbaV0uc3Vic3RyaW5nKHBvcyArIDEpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kO1xuICAgICAgICBpZiAoIWZvdW5kICYmIGxvb2t1cEZyb21IYXNoSW5kZXggPiAtMSkge1xuICAgICAgICAgIGNvbnN0IGxhbmd1YWdlID0gaGFzaC5tYXRjaCgvXFwvKFthLXpBLVotXSopL2cpO1xuICAgICAgICAgIGlmICghQXJyYXkuaXNBcnJheShsYW5ndWFnZSkpIHJldHVybiB1bmRlZmluZWQ7XG4gICAgICAgICAgY29uc3QgaW5kZXggPSB0eXBlb2YgbG9va3VwRnJvbUhhc2hJbmRleCA9PT0gJ251bWJlcicgPyBsb29rdXBGcm9tSGFzaEluZGV4IDogMDtcbiAgICAgICAgICByZXR1cm4gbGFuZ3VhZ2VbaW5kZXhdPy5yZXBsYWNlKCcvJywgJycpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmb3VuZDtcbiAgfVxufTtcblxubGV0IGhhc0xvY2FsU3RvcmFnZVN1cHBvcnQgPSBudWxsO1xuY29uc3QgbG9jYWxTdG9yYWdlQXZhaWxhYmxlID0gKCkgPT4ge1xuICBpZiAoaGFzTG9jYWxTdG9yYWdlU3VwcG9ydCAhPT0gbnVsbCkgcmV0dXJuIGhhc0xvY2FsU3RvcmFnZVN1cHBvcnQ7XG4gIHRyeSB7XG4gICAgaGFzTG9jYWxTdG9yYWdlU3VwcG9ydCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhbFN0b3JhZ2UgIT09IG51bGw7XG4gICAgaWYgKCFoYXNMb2NhbFN0b3JhZ2VTdXBwb3J0KSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IHRlc3RLZXkgPSAnaTE4bmV4dC50cmFuc2xhdGUuYm9vJztcbiAgICB3aW5kb3cubG9jYWxTdG9yYWdlLnNldEl0ZW0odGVzdEtleSwgJ2ZvbycpO1xuICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSh0ZXN0S2V5KTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGhhc0xvY2FsU3RvcmFnZVN1cHBvcnQgPSBmYWxzZTtcbiAgfVxuICByZXR1cm4gaGFzTG9jYWxTdG9yYWdlU3VwcG9ydDtcbn07XG52YXIgbG9jYWxTdG9yYWdlID0ge1xuICBuYW1lOiAnbG9jYWxTdG9yYWdlJyxcbiAgLy8gRGVjb25zdHJ1Y3QgdGhlIG9wdGlvbnMgb2JqZWN0IGFuZCBleHRyYWN0IHRoZSBsb29rdXBMb2NhbFN0b3JhZ2UgcHJvcGVydHlcbiAgbG9va3VwKF9yZWYpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwTG9jYWxTdG9yYWdlXG4gICAgfSA9IF9yZWY7XG4gICAgaWYgKGxvb2t1cExvY2FsU3RvcmFnZSAmJiBsb2NhbFN0b3JhZ2VBdmFpbGFibGUoKSkge1xuICAgICAgcmV0dXJuIHdpbmRvdy5sb2NhbFN0b3JhZ2UuZ2V0SXRlbShsb29rdXBMb2NhbFN0b3JhZ2UpIHx8IHVuZGVmaW5lZDsgLy8gVW5kZWZpbmVkIGVuc3VyZXMgdHlwZSBjb25zaXN0ZW5jeSB3aXRoIHRoZSBwcmV2aW91cyB2ZXJzaW9uIG9mIHRoaXMgZnVuY3Rpb25cbiAgICB9XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgfSxcbiAgLy8gRGVjb25zdHJ1Y3QgdGhlIG9wdGlvbnMgb2JqZWN0IGFuZCBleHRyYWN0IHRoZSBsb29rdXBMb2NhbFN0b3JhZ2UgcHJvcGVydHlcbiAgY2FjaGVVc2VyTGFuZ3VhZ2UobG5nLCBfcmVmMikge1xuICAgIGxldCB7XG4gICAgICBsb29rdXBMb2NhbFN0b3JhZ2VcbiAgICB9ID0gX3JlZjI7XG4gICAgaWYgKGxvb2t1cExvY2FsU3RvcmFnZSAmJiBsb2NhbFN0b3JhZ2VBdmFpbGFibGUoKSkge1xuICAgICAgd2luZG93LmxvY2FsU3RvcmFnZS5zZXRJdGVtKGxvb2t1cExvY2FsU3RvcmFnZSwgbG5nKTtcbiAgICB9XG4gIH1cbn07XG5cbmxldCBoYXNTZXNzaW9uU3RvcmFnZVN1cHBvcnQgPSBudWxsO1xuY29uc3Qgc2Vzc2lvblN0b3JhZ2VBdmFpbGFibGUgPSAoKSA9PiB7XG4gIGlmIChoYXNTZXNzaW9uU3RvcmFnZVN1cHBvcnQgIT09IG51bGwpIHJldHVybiBoYXNTZXNzaW9uU3RvcmFnZVN1cHBvcnQ7XG4gIHRyeSB7XG4gICAgaGFzU2Vzc2lvblN0b3JhZ2VTdXBwb3J0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LnNlc3Npb25TdG9yYWdlICE9PSBudWxsO1xuICAgIGlmICghaGFzU2Vzc2lvblN0b3JhZ2VTdXBwb3J0KSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IHRlc3RLZXkgPSAnaTE4bmV4dC50cmFuc2xhdGUuYm9vJztcbiAgICB3aW5kb3cuc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSh0ZXN0S2V5LCAnZm9vJyk7XG4gICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0odGVzdEtleSk7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBoYXNTZXNzaW9uU3RvcmFnZVN1cHBvcnQgPSBmYWxzZTtcbiAgfVxuICByZXR1cm4gaGFzU2Vzc2lvblN0b3JhZ2VTdXBwb3J0O1xufTtcbnZhciBzZXNzaW9uU3RvcmFnZSA9IHtcbiAgbmFtZTogJ3Nlc3Npb25TdG9yYWdlJyxcbiAgbG9va3VwKF9yZWYpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwU2Vzc2lvblN0b3JhZ2VcbiAgICB9ID0gX3JlZjtcbiAgICBpZiAobG9va3VwU2Vzc2lvblN0b3JhZ2UgJiYgc2Vzc2lvblN0b3JhZ2VBdmFpbGFibGUoKSkge1xuICAgICAgcmV0dXJuIHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5nZXRJdGVtKGxvb2t1cFNlc3Npb25TdG9yYWdlKSB8fCB1bmRlZmluZWQ7XG4gICAgfVxuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH0sXG4gIGNhY2hlVXNlckxhbmd1YWdlKGxuZywgX3JlZjIpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwU2Vzc2lvblN0b3JhZ2VcbiAgICB9ID0gX3JlZjI7XG4gICAgaWYgKGxvb2t1cFNlc3Npb25TdG9yYWdlICYmIHNlc3Npb25TdG9yYWdlQXZhaWxhYmxlKCkpIHtcbiAgICAgIHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5zZXRJdGVtKGxvb2t1cFNlc3Npb25TdG9yYWdlLCBsbmcpO1xuICAgIH1cbiAgfVxufTtcblxudmFyIG5hdmlnYXRvciQxID0ge1xuICBuYW1lOiAnbmF2aWdhdG9yJyxcbiAgbG9va3VwKG9wdGlvbnMpIHtcbiAgICBjb25zdCBmb3VuZCA9IFtdO1xuICAgIGlmICh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3Qge1xuICAgICAgICBsYW5ndWFnZXMsXG4gICAgICAgIHVzZXJMYW5ndWFnZSxcbiAgICAgICAgbGFuZ3VhZ2VcbiAgICAgIH0gPSBuYXZpZ2F0b3I7XG4gICAgICBpZiAobGFuZ3VhZ2VzKSB7XG4gICAgICAgIC8vIGNocm9tZSBvbmx5OyBub3QgYW4gYXJyYXksIHNvIGNhbid0IHVzZSAucHVzaC5hcHBseSBpbnN0ZWFkIG9mIGl0ZXJhdGluZ1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxhbmd1YWdlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIGZvdW5kLnB1c2gobGFuZ3VhZ2VzW2ldKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKHVzZXJMYW5ndWFnZSkge1xuICAgICAgICBmb3VuZC5wdXNoKHVzZXJMYW5ndWFnZSk7XG4gICAgICB9XG4gICAgICBpZiAobGFuZ3VhZ2UpIHtcbiAgICAgICAgZm91bmQucHVzaChsYW5ndWFnZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmb3VuZC5sZW5ndGggPiAwID8gZm91bmQgOiB1bmRlZmluZWQ7XG4gIH1cbn07XG5cbnZhciBodG1sVGFnID0ge1xuICBuYW1lOiAnaHRtbFRhZycsXG4gIC8vIERlY29uc3RydWN0IHRoZSBvcHRpb25zIG9iamVjdCBhbmQgZXh0cmFjdCB0aGUgaHRtbFRhZyBwcm9wZXJ0eVxuICBsb29rdXAoX3JlZikge1xuICAgIGxldCB7XG4gICAgICBodG1sVGFnXG4gICAgfSA9IF9yZWY7XG4gICAgbGV0IGZvdW5kO1xuICAgIGNvbnN0IGludGVybmFsSHRtbFRhZyA9IGh0bWxUYWcgfHwgKHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcgPyBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQgOiBudWxsKTtcbiAgICBpZiAoaW50ZXJuYWxIdG1sVGFnICYmIHR5cGVvZiBpbnRlcm5hbEh0bWxUYWcuZ2V0QXR0cmlidXRlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBmb3VuZCA9IGludGVybmFsSHRtbFRhZy5nZXRBdHRyaWJ1dGUoJ2xhbmcnKTtcbiAgICB9XG4gICAgcmV0dXJuIGZvdW5kO1xuICB9XG59O1xuXG52YXIgcGF0aCA9IHtcbiAgbmFtZTogJ3BhdGgnLFxuICAvLyBEZWNvbnN0cnVjdCB0aGUgb3B0aW9ucyBvYmplY3QgYW5kIGV4dHJhY3QgdGhlIGxvb2t1cEZyb21QYXRoSW5kZXggcHJvcGVydHlcbiAgbG9va3VwKF9yZWYpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwRnJvbVBhdGhJbmRleFxuICAgIH0gPSBfcmVmO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICBjb25zdCBsYW5ndWFnZSA9IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZS5tYXRjaCgvXFwvKFthLXpBLVotXSopL2cpO1xuICAgIGlmICghQXJyYXkuaXNBcnJheShsYW5ndWFnZSkpIHJldHVybiB1bmRlZmluZWQ7XG4gICAgY29uc3QgaW5kZXggPSB0eXBlb2YgbG9va3VwRnJvbVBhdGhJbmRleCA9PT0gJ251bWJlcicgPyBsb29rdXBGcm9tUGF0aEluZGV4IDogMDtcbiAgICByZXR1cm4gbGFuZ3VhZ2VbaW5kZXhdPy5yZXBsYWNlKCcvJywgJycpO1xuICB9XG59O1xuXG52YXIgc3ViZG9tYWluID0ge1xuICBuYW1lOiAnc3ViZG9tYWluJyxcbiAgbG9va3VwKF9yZWYpIHtcbiAgICBsZXQge1xuICAgICAgbG9va3VwRnJvbVN1YmRvbWFpbkluZGV4XG4gICAgfSA9IF9yZWY7XG4gICAgLy8gSWYgZ2l2ZW4gZ2V0IHRoZSBzdWJkb21haW4gaW5kZXggZWxzZSAxXG4gICAgY29uc3QgaW50ZXJuYWxMb29rdXBGcm9tU3ViZG9tYWluSW5kZXggPSB0eXBlb2YgbG9va3VwRnJvbVN1YmRvbWFpbkluZGV4ID09PSAnbnVtYmVyJyA/IGxvb2t1cEZyb21TdWJkb21haW5JbmRleCArIDEgOiAxO1xuICAgIC8vIGdldCBhbGwgbWF0Y2hlcyBpZiB3aW5kb3cubG9jYXRpb24uIGlzIGV4aXN0aW5nXG4gICAgLy8gZmlyc3QgaXRlbSBvZiBtYXRjaCBpcyB0aGUgbWF0Y2ggaXRzZWxmIGFuZCB0aGUgc2Vjb25kIGlzIHRoZSBmaXJzdCBncm91cCBtYXRjaCB3aGljaCBzaG91bGQgYmUgdGhlIGZpcnN0IHN1YmRvbWFpbiBtYXRjaFxuICAgIC8vIGlzIHRoZSBob3N0bmFtZSBubyBwdWJsaWMgZG9tYWluIGdldCB0aGUgb3Igb3B0aW9uIG9mIGxvY2FsaG9zdFxuICAgIGNvbnN0IGxhbmd1YWdlID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uPy5ob3N0bmFtZT8ubWF0Y2goL14oXFx3ezIsNX0pXFwuKChbYS16MC05LV17MSw2M31cXC5bYS16XXsyLDZ9KXxsb2NhbGhvc3QpL2kpO1xuXG4gICAgLy8gaWYgdGhlcmUgaXMgbm8gbWF0Y2ggKG51bGwpIHJldHVybiB1bmRlZmluZWRcbiAgICBpZiAoIWxhbmd1YWdlKSByZXR1cm4gdW5kZWZpbmVkO1xuICAgIC8vIHJldHVybiB0aGUgZ2l2ZW4gZ3JvdXAgbWF0Y2hcbiAgICByZXR1cm4gbGFuZ3VhZ2VbaW50ZXJuYWxMb29rdXBGcm9tU3ViZG9tYWluSW5kZXhdO1xuICB9XG59O1xuXG4vLyBzb21lIGVudmlyb25tZW50cywgdGhyb3dzIHdoZW4gYWNjZXNzaW5nIGRvY3VtZW50LmNvb2tpZVxubGV0IGNhbkNvb2tpZXMgPSBmYWxzZTtcbnRyeSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgZG9jdW1lbnQuY29va2llO1xuICBjYW5Db29raWVzID0gdHJ1ZTtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWVtcHR5XG59IGNhdGNoIChlKSB7fVxuY29uc3Qgb3JkZXIgPSBbJ3F1ZXJ5c3RyaW5nJywgJ2Nvb2tpZScsICdsb2NhbFN0b3JhZ2UnLCAnc2Vzc2lvblN0b3JhZ2UnLCAnbmF2aWdhdG9yJywgJ2h0bWxUYWcnXTtcbmlmICghY2FuQ29va2llcykgb3JkZXIuc3BsaWNlKDEsIDEpO1xuY29uc3QgZ2V0RGVmYXVsdHMgPSAoKSA9PiAoe1xuICBvcmRlcixcbiAgbG9va3VwUXVlcnlzdHJpbmc6ICdsbmcnLFxuICBsb29rdXBDb29raWU6ICdpMThuZXh0JyxcbiAgbG9va3VwTG9jYWxTdG9yYWdlOiAnaTE4bmV4dExuZycsXG4gIGxvb2t1cFNlc3Npb25TdG9yYWdlOiAnaTE4bmV4dExuZycsXG4gIC8vIGNhY2hlIHVzZXIgbGFuZ3VhZ2VcbiAgY2FjaGVzOiBbJ2xvY2FsU3RvcmFnZSddLFxuICBleGNsdWRlQ2FjaGVGb3I6IFsnY2ltb2RlJ10sXG4gIC8vIGNvb2tpZU1pbnV0ZXM6IDEwLFxuICAvLyBjb29raWVEb21haW46ICdteURvbWFpbidcblxuICBjb252ZXJ0RGV0ZWN0ZWRMYW5ndWFnZTogbCA9PiBsXG59KTtcbmNsYXNzIEJyb3dzZXIge1xuICBjb25zdHJ1Y3RvcihzZXJ2aWNlcykge1xuICAgIGxldCBvcHRpb25zID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiB7fTtcbiAgICB0aGlzLnR5cGUgPSAnbGFuZ3VhZ2VEZXRlY3Rvcic7XG4gICAgdGhpcy5kZXRlY3RvcnMgPSB7fTtcbiAgICB0aGlzLmluaXQoc2VydmljZXMsIG9wdGlvbnMpO1xuICB9XG4gIGluaXQoKSB7XG4gICAgbGV0IHNlcnZpY2VzID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiB7XG4gICAgICBsYW5ndWFnZVV0aWxzOiB7fVxuICAgIH07XG4gICAgbGV0IG9wdGlvbnMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHt9O1xuICAgIGxldCBpMThuT3B0aW9ucyA9IGFyZ3VtZW50cy5sZW5ndGggPiAyICYmIGFyZ3VtZW50c1syXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzJdIDoge307XG4gICAgdGhpcy5zZXJ2aWNlcyA9IHNlcnZpY2VzO1xuICAgIHRoaXMub3B0aW9ucyA9IGRlZmF1bHRzKG9wdGlvbnMsIHRoaXMub3B0aW9ucyB8fCB7fSwgZ2V0RGVmYXVsdHMoKSk7XG4gICAgaWYgKHR5cGVvZiB0aGlzLm9wdGlvbnMuY29udmVydERldGVjdGVkTGFuZ3VhZ2UgPT09ICdzdHJpbmcnICYmIHRoaXMub3B0aW9ucy5jb252ZXJ0RGV0ZWN0ZWRMYW5ndWFnZS5pbmRleE9mKCcxNTg5NycpID4gLTEpIHtcbiAgICAgIHRoaXMub3B0aW9ucy5jb252ZXJ0RGV0ZWN0ZWRMYW5ndWFnZSA9IGwgPT4gbC5yZXBsYWNlKCctJywgJ18nKTtcbiAgICB9XG5cbiAgICAvLyBiYWNrd2FyZHMgY29tcGF0aWJpbGl0eVxuICAgIGlmICh0aGlzLm9wdGlvbnMubG9va3VwRnJvbVVybEluZGV4KSB0aGlzLm9wdGlvbnMubG9va3VwRnJvbVBhdGhJbmRleCA9IHRoaXMub3B0aW9ucy5sb29rdXBGcm9tVXJsSW5kZXg7XG4gICAgdGhpcy5pMThuT3B0aW9ucyA9IGkxOG5PcHRpb25zO1xuICAgIHRoaXMuYWRkRGV0ZWN0b3IoY29va2llJDEpO1xuICAgIHRoaXMuYWRkRGV0ZWN0b3IocXVlcnlzdHJpbmcpO1xuICAgIHRoaXMuYWRkRGV0ZWN0b3IobG9jYWxTdG9yYWdlKTtcbiAgICB0aGlzLmFkZERldGVjdG9yKHNlc3Npb25TdG9yYWdlKTtcbiAgICB0aGlzLmFkZERldGVjdG9yKG5hdmlnYXRvciQxKTtcbiAgICB0aGlzLmFkZERldGVjdG9yKGh0bWxUYWcpO1xuICAgIHRoaXMuYWRkRGV0ZWN0b3IocGF0aCk7XG4gICAgdGhpcy5hZGREZXRlY3RvcihzdWJkb21haW4pO1xuICAgIHRoaXMuYWRkRGV0ZWN0b3IoaGFzaCk7XG4gIH1cbiAgYWRkRGV0ZWN0b3IoZGV0ZWN0b3IpIHtcbiAgICB0aGlzLmRldGVjdG9yc1tkZXRlY3Rvci5uYW1lXSA9IGRldGVjdG9yO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGRldGVjdCgpIHtcbiAgICBsZXQgZGV0ZWN0aW9uT3JkZXIgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IHRoaXMub3B0aW9ucy5vcmRlcjtcbiAgICBsZXQgZGV0ZWN0ZWQgPSBbXTtcbiAgICBkZXRlY3Rpb25PcmRlci5mb3JFYWNoKGRldGVjdG9yTmFtZSA9PiB7XG4gICAgICBpZiAodGhpcy5kZXRlY3RvcnNbZGV0ZWN0b3JOYW1lXSkge1xuICAgICAgICBsZXQgbG9va3VwID0gdGhpcy5kZXRlY3RvcnNbZGV0ZWN0b3JOYW1lXS5sb29rdXAodGhpcy5vcHRpb25zKTtcbiAgICAgICAgaWYgKGxvb2t1cCAmJiB0eXBlb2YgbG9va3VwID09PSAnc3RyaW5nJykgbG9va3VwID0gW2xvb2t1cF07XG4gICAgICAgIGlmIChsb29rdXApIGRldGVjdGVkID0gZGV0ZWN0ZWQuY29uY2F0KGxvb2t1cCk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgZGV0ZWN0ZWQgPSBkZXRlY3RlZC5maWx0ZXIoZCA9PiBkICE9PSB1bmRlZmluZWQgJiYgZCAhPT0gbnVsbCAmJiAhaGFzWFNTKGQpKS5tYXAoZCA9PiB0aGlzLm9wdGlvbnMuY29udmVydERldGVjdGVkTGFuZ3VhZ2UoZCkpO1xuICAgIGlmICh0aGlzLnNlcnZpY2VzICYmIHRoaXMuc2VydmljZXMubGFuZ3VhZ2VVdGlscyAmJiB0aGlzLnNlcnZpY2VzLmxhbmd1YWdlVXRpbHMuZ2V0QmVzdE1hdGNoRnJvbUNvZGVzKSByZXR1cm4gZGV0ZWN0ZWQ7IC8vIG5ldyBpMThuZXh0IHYxOS41LjBcbiAgICByZXR1cm4gZGV0ZWN0ZWQubGVuZ3RoID4gMCA/IGRldGVjdGVkWzBdIDogbnVsbDsgLy8gYSBsaXR0bGUgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxuICB9XG4gIGNhY2hlVXNlckxhbmd1YWdlKGxuZykge1xuICAgIGxldCBjYWNoZXMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHRoaXMub3B0aW9ucy5jYWNoZXM7XG4gICAgaWYgKCFjYWNoZXMpIHJldHVybjtcbiAgICBpZiAodGhpcy5vcHRpb25zLmV4Y2x1ZGVDYWNoZUZvciAmJiB0aGlzLm9wdGlvbnMuZXhjbHVkZUNhY2hlRm9yLmluZGV4T2YobG5nKSA+IC0xKSByZXR1cm47XG4gICAgY2FjaGVzLmZvckVhY2goY2FjaGVOYW1lID0+IHtcbiAgICAgIGlmICh0aGlzLmRldGVjdG9yc1tjYWNoZU5hbWVdKSB0aGlzLmRldGVjdG9yc1tjYWNoZU5hbWVdLmNhY2hlVXNlckxhbmd1YWdlKGxuZywgdGhpcy5vcHRpb25zKTtcbiAgICB9KTtcbiAgfVxufVxuQnJvd3Nlci50eXBlID0gJ2xhbmd1YWdlRGV0ZWN0b3InO1xuXG5leHBvcnQgeyBCcm93c2VyIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\n");

/***/ })

};
;