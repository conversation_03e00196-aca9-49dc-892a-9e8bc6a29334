'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Edit, 
  FileText, 
  Users,
  Download,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  mockBeneficiaries, 
  zakatCategoryLabels, 
  searchBeneficiaries,
  getBeneficiariesByStatus,
  getBeneficiariesByCategory,
  getBeneficiaryStats
} from '@/lib/mock-data'
import type { Beneficiary, ZakatCategory, BeneficiaryStatus } from '@/lib/types'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

const statusLabels: Record<BeneficiaryStatus, { ar: string; en: string; variant: any }> = {
  pending_verification: { ar: 'في انتظار التحقق', en: 'Pending Verification', variant: 'secondary' },
  under_review: { ar: 'قيد المراجعة', en: 'Under Review', variant: 'default' },
  approved: { ar: 'موافق عليه', en: 'Approved', variant: 'success' },
  rejected: { ar: 'مرفوض', en: 'Rejected', variant: 'destructive' },
  suspended: { ar: 'معلق', en: 'Suspended', variant: 'warning' },
  inactive: { ar: 'غير نشط', en: 'Inactive', variant: 'outline' }
}

export default function BeneficiariesPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<BeneficiaryStatus | 'all'>('all')
  const [categoryFilter, setCategoryFilter] = useState<ZakatCategory | 'all'>('all')

  if (!session?.user) {
    return null
  }

  // Check if user has access to beneficiary management
  const hasAccess = ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'].includes(session.user.role)
  
  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
            <p className="text-muted-foreground">{t('no_beneficiary_access')}</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Filter beneficiaries based on search and filters
  let filteredBeneficiaries = mockBeneficiaries

  if (searchTerm) {
    filteredBeneficiaries = searchBeneficiaries(searchTerm)
  }

  if (statusFilter !== 'all') {
    filteredBeneficiaries = filteredBeneficiaries.filter(b => b.status === statusFilter)
  }

  if (categoryFilter !== 'all') {
    filteredBeneficiaries = filteredBeneficiaries.filter(b => 
      b.zakatCategories.includes(categoryFilter) || b.primaryCategory === categoryFilter
    )
  }

  const stats = getBeneficiaryStats()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const StatusBadge = ({ status }: { status: BeneficiaryStatus }) => {
    const label = statusLabels[status]
    return (
      <Badge variant={label.variant}>
        {i18n.language === 'ar' ? label.ar : label.en}
      </Badge>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('beneficiary_management')}
            </h1>
            <p className="text-muted-foreground">
              {t('beneficiary_management_desc')}
            </p>
          </div>
          <Button asChild>
            <Link href="/beneficiaries/new">
              <Plus className="mr-2 h-4 w-4" />
              {t('register_new_beneficiary')}
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('total_beneficiaries')}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.pending} {t('pending_verification_count')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('approved_beneficiaries')}</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.approved}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((stats.approved / stats.total) * 100)}% {t('of_total')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('under_review_beneficiaries')}</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.underReview}</div>
              <p className="text-xs text-muted-foreground">
                {t('needs_review')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('total_distributions')}</CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalDistributed)}</div>
              <p className="text-xs text-muted-foreground">
                {t('average_distribution')} {formatCurrency(stats.averageDistribution)} {t('per_beneficiary')}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              {t('search_and_filter')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="flex-1">
                <Input
                  placeholder={t('search_placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>

              <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as BeneficiaryStatus | 'all')}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder={t('filter_by_status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('all_statuses')}</SelectItem>
                  {Object.entries(statusLabels).map(([status, label]) => (
                    <SelectItem key={status} value={status}>
                      {t(status as any)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={categoryFilter} onValueChange={(value) => setCategoryFilter(value as ZakatCategory | 'all')}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder={t('filter_by_category')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('all_categories')}</SelectItem>
                  {Object.entries(zakatCategoryLabels).map(([category, label]) => (
                    <SelectItem key={category} value={category}>
                      {t(category as any)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Beneficiaries Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{t('beneficiary_list')}</CardTitle>
                <CardDescription>
                  {t('showing_results', { count: filteredBeneficiaries.length, total: stats.total })}
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                {t('export')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('name')}</TableHead>
                    <TableHead>{t('national_id')}</TableHead>
                    <TableHead>{t('primary_category')}</TableHead>
                    <TableHead>{t('status')}</TableHead>
                    <TableHead>{t('registration_date')}</TableHead>
                    <TableHead>{t('total_received')}</TableHead>
                    <TableHead className="text-center">{t('actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBeneficiaries.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {t('no_results_found')}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBeneficiaries.map((beneficiary) => (
                      <TableRow key={beneficiary.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {i18n.language === 'ar' ? beneficiary.fullNameAr : beneficiary.fullNameEn}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {beneficiary.phoneNumber}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono">
                          {beneficiary.nationalId}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {t(beneficiary.primaryCategory)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={beneficiary.status} />
                        </TableCell>
                        <TableCell>
                          {format(beneficiary.registrationDate, 'dd/MM/yyyy', {
                            locale: i18n.language === 'ar' ? ar : undefined
                          })}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(beneficiary.totalReceived)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/beneficiaries/${beneficiary.id}`}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  {t('view_profile')}
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/beneficiaries/${beneficiary.id}/edit`}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  {t('edit')}
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/beneficiaries/${beneficiary.id}/case`}>
                                  <FileText className="mr-2 h-4 w-4" />
                                  {t('manage_case')}
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
