/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"pending\": \"في الانتظار\",\n            \"completed\": \"مكتمل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"welcome\": \"مرحباً\",\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Beneficiary Management\n            \"beneficiaries\": \"المستفيدين\",\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"national_id\": \"رقم الهوية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"total_received\": \"إجمالي المستلم\",\n            \"actions\": \"الإجراءات\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"edit\": \"تعديل\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            \"documents\": \"المستندات\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            \"distributions\": \"توزيعات\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"pending\": \"Pending\",\n            \"completed\": \"Completed\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"welcome\": \"Welcome\",\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiaries\": \"Beneficiaries\",\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"national_id\": \"National ID\",\n            \"registration_date\": \"Registration Date\",\n            \"total_received\": \"Total Received\",\n            \"actions\": \"Actions\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"edit\": \"Edit\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            \"documents\": \"Documents\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            \"distributions\": \"Distributions\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"pending_verification\": \"Pending Verification\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"42bb61ef2a3f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQyYmI2MWVmMmEzZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();