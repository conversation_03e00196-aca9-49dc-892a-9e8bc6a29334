"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ivahr_OneDrive_Documents_VCode_projects_zakat_deepagent_zakat_management_system_app_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ivahr_OneDrive_Documents_VCode_projects_zakat_deepagent_zakat_management_system_app_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mock-data */ \"(rsc)/./lib/mock-data.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // Find user in mock data\n                const user = (0,_lib_mock_data__WEBPACK_IMPORTED_MODULE_2__.getUserByEmail)(credentials.email);\n                if (!user) {\n                    return null;\n                }\n                // For demo purposes, we'll use simple password check\n                // In production, this should be properly hashed and compared\n                const isValidPassword = credentials.password === \"johndoe123\" || credentials.email === \"<EMAIL>\";\n                if (!isValidPassword) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.fullName,\n                    role: user.role,\n                    nationalId: user.nationalId,\n                    accountStatus: user.accountStatus\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.nationalId = user.nationalId;\n                token.accountStatus = user.accountStatus;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.nationalId = token.nationalId;\n                session.user.accountStatus = token.accountStatus;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\",\n        error: \"/auth/error\"\n    }\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBeneficiariesByCategory: () => (/* binding */ getBeneficiariesByCategory),\n/* harmony export */   getBeneficiariesByStatus: () => (/* binding */ getBeneficiariesByStatus),\n/* harmony export */   getBeneficiaryById: () => (/* binding */ getBeneficiaryById),\n/* harmony export */   getBeneficiaryStats: () => (/* binding */ getBeneficiaryStats),\n/* harmony export */   getRequestsByUserId: () => (/* binding */ getRequestsByUserId),\n/* harmony export */   getTasksByUserId: () => (/* binding */ getTasksByUserId),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   mockAssistanceRequests: () => (/* binding */ mockAssistanceRequests),\n/* harmony export */   mockAssistanceTypes: () => (/* binding */ mockAssistanceTypes),\n/* harmony export */   mockBeneficiaries: () => (/* binding */ mockBeneficiaries),\n/* harmony export */   mockDashboardStats: () => (/* binding */ mockDashboardStats),\n/* harmony export */   mockTasks: () => (/* binding */ mockTasks),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers),\n/* harmony export */   searchBeneficiaries: () => (/* binding */ searchBeneficiaries),\n/* harmony export */   zakatCategoryLabels: () => (/* binding */ zakatCategoryLabels)\n/* harmony export */ });\n// Mock Users for each role\nconst mockUsers = [\n    // Test admin account\n    {\n        id: \"1\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"John Doe\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1980-01-01\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"system_admin\",\n        createdAt: new Date(\"2023-01-01\"),\n        lastLogin: new Date()\n    },\n    // Zakat Applicants\n    {\n        id: \"2\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Ahmed Salem Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1985-05-15\"),\n        phoneNumber: \"+************\",\n        address: \"Jeddah, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-02-01\"),\n        lastLogin: new Date(),\n        profileId: \"profile-2\"\n    },\n    {\n        id: \"3\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Fatima Omar Al-Zahra\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1990-08-20\"),\n        phoneNumber: \"+************\",\n        address: \"Dammam, Saudi Arabia\",\n        accountStatus: \"pending_approval\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-03-01\"),\n        profileId: \"profile-3\"\n    },\n    // Staff Members\n    {\n        id: \"4\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Sara Abdullah Al-Mansouri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1988-03-10\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"reception_staff\",\n        createdAt: new Date(\"2022-01-15\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"5\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Mohammed Hassan Al-Qadiri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1982-07-25\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"researcher\",\n        createdAt: new Date(\"2022-02-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"6\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Khalid Ahmed Al-Othman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1975-12-05\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"banking_expert\",\n        createdAt: new Date(\"2021-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"7\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Nasser Fahad Al-Saud\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1970-04-18\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"department_head\",\n        createdAt: new Date(\"2020-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"8\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Abdulaziz Mohammed Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1968-09-12\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"admin_manager\",\n        createdAt: new Date(\"2019-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"9\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"His Excellency Abdullah bin Salman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1965-02-28\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"minister\",\n        createdAt: new Date(\"2018-01-01\"),\n        lastLogin: new Date()\n    }\n];\n// Mock Assistance Types\nconst mockAssistanceTypes = [\n    {\n        id: \"financial-support\",\n        nameAr: \"المساعدة المالية العامة\",\n        nameEn: \"General Financial Support\",\n        descriptionAr: \"مساعدة مالية للأسر المحتاجة\",\n        descriptionEn: \"Financial assistance for needy families\",\n        maxAmount: 25000,\n        requiredDocuments: [\n            {\n                id: \"salary-certificate\",\n                nameAr: \"شهادة راتب\",\n                nameEn: \"Salary Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            },\n            {\n                id: \"bank-statement\",\n                nameAr: \"كشف حساب بنكي\",\n                nameEn: \"Bank Statement\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"monthlyIncome\",\n                condition: \"less_than\",\n                value: 5000,\n                nationality: \"Saudi Arabia\"\n            }\n        ],\n        isActive: true,\n        category: \"Financial\"\n    },\n    {\n        id: \"medical-support\",\n        nameAr: \"المساعدة الطبية\",\n        nameEn: \"Medical Support\",\n        descriptionAr: \"مساعدة لتغطية التكاليف الطبية\",\n        descriptionEn: \"Assistance to cover medical expenses\",\n        maxAmount: 50000,\n        requiredDocuments: [\n            {\n                id: \"medical-report\",\n                nameAr: \"التقرير الطبي\",\n                nameEn: \"Medical Report\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 3072\n            },\n            {\n                id: \"medical-bills\",\n                nameAr: \"الفواتير الطبية\",\n                nameEn: \"Medical Bills\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [],\n        isActive: true,\n        category: \"Medical\"\n    },\n    {\n        id: \"education-support\",\n        nameAr: \"المساعدة التعليمية\",\n        nameEn: \"Education Support\",\n        descriptionAr: \"مساعدة لتغطية تكاليف التعليم\",\n        descriptionEn: \"Assistance to cover education expenses\",\n        maxAmount: 15000,\n        requiredDocuments: [\n            {\n                id: \"enrollment-certificate\",\n                nameAr: \"شهادة قيد\",\n                nameEn: \"Enrollment Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"familyMembersCount\",\n                condition: \"greater_than\",\n                value: 0\n            }\n        ],\n        isActive: true,\n        category: \"Education\"\n    }\n];\n// Mock Assistance Requests\nconst mockAssistanceRequests = [\n    {\n        id: \"req-001\",\n        userId: \"2\",\n        assistanceType: mockAssistanceTypes[0],\n        requestedAmount: 15000,\n        approvedAmount: 12000,\n        description: \"نحتاج إلى مساعدة مالية لتغطية تكاليف المعيشة بعد توقف العمل\",\n        status: \"approved\",\n        submissionDate: new Date(\"2023-10-15\"),\n        lastUpdateDate: new Date(\"2023-11-01\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-001\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-10-15\"),\n                stageEndDate: new Date(\"2023-10-16\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-10-16\"),\n                notes: \"المستندات كاملة والحالة تستدعي المساعدة\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-001\",\n                stage: \"approved\",\n                reviewerId: \"8\",\n                reviewerName: \"Abdulaziz Mohammed Al-Rashid\",\n                stageStartDate: new Date(\"2023-10-30\"),\n                stageEndDate: new Date(\"2023-11-01\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-01\"),\n                notes: \"تمت الموافقة على مبلغ 12,000 ريال\"\n            }\n        ],\n        priority: \"medium\"\n    },\n    {\n        id: \"req-002\",\n        userId: \"3\",\n        assistanceType: mockAssistanceTypes[1],\n        requestedAmount: 35000,\n        description: \"نحتاج مساعدة لتغطية تكاليف علاج والدي في المستشفى\",\n        status: \"researcher_review\",\n        submissionDate: new Date(\"2023-11-10\"),\n        lastUpdateDate: new Date(\"2023-11-12\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-002\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-11-10\"),\n                stageEndDate: new Date(\"2023-11-11\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-11\"),\n                notes: \"تم مراجعة الطلب وإحالته للباحث\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-002\",\n                stage: \"researcher_review\",\n                reviewerId: \"5\",\n                reviewerName: \"Mohammed Hassan Al-Qadiri\",\n                stageStartDate: new Date(\"2023-11-11\"),\n                notes: \"قيد المراجعة والتحقق من المستندات الطبية\"\n            }\n        ],\n        priority: \"high\"\n    }\n];\n// Mock Tasks for different roles\nconst mockTasks = {\n    reception_staff: [\n        {\n            id: \"task-1\",\n            assignedTo: \"4\",\n            requestId: \"req-003\",\n            type: \"profile_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + ********),\n            status: \"pending\",\n            createdDate: new Date()\n        },\n        {\n            id: \"task-2\",\n            assignedTo: \"4\",\n            requestId: \"req-004\",\n            type: \"request_review\",\n            priority: \"high\",\n            status: \"in_progress\",\n            createdDate: new Date(Date.now() - ********)\n        }\n    ],\n    researcher: [\n        {\n            id: \"task-3\",\n            assignedTo: \"5\",\n            requestId: \"req-002\",\n            type: \"request_review\",\n            priority: \"high\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"in_progress\",\n            createdDate: new Date(\"2023-11-11\")\n        }\n    ],\n    banking_expert: [\n        {\n            id: \"task-4\",\n            assignedTo: \"6\",\n            requestId: \"req-005\",\n            type: \"request_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"pending\",\n            createdDate: new Date()\n        }\n    ],\n    department_head: [],\n    admin_manager: [],\n    minister: [],\n    zakat_applicant: [],\n    system_admin: []\n};\n// Mock Dashboard Stats for different roles\nconst mockDashboardStats = {\n    reception_staff: {\n        totalRequests: 45,\n        pendingReview: 8,\n        approvedToday: 3,\n        rejectedToday: 1,\n        averageProcessingDays: 2,\n        totalUsers: 150\n    },\n    researcher: {\n        totalRequests: 32,\n        pendingReview: 5,\n        approvedToday: 2,\n        rejectedToday: 0,\n        averageProcessingDays: 3,\n        totalUsers: 150\n    },\n    banking_expert: {\n        totalRequests: 28,\n        pendingReview: 4,\n        approvedToday: 1,\n        rejectedToday: 1,\n        averageProcessingDays: 4,\n        totalUsers: 150\n    },\n    department_head: {\n        totalRequests: 15,\n        pendingReview: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        averageProcessingDays: 5,\n        totalUsers: 150\n    },\n    admin_manager: {\n        totalRequests: 120,\n        pendingReview: 8,\n        approvedToday: 5,\n        rejectedToday: 2,\n        averageProcessingDays: 12,\n        totalUsers: 150\n    },\n    minister: {\n        totalRequests: 8,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 7,\n        totalUsers: 150\n    },\n    zakat_applicant: {\n        totalRequests: 3,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 14,\n        totalUsers: 1\n    },\n    system_admin: {\n        totalRequests: 200,\n        pendingReview: 25,\n        approvedToday: 12,\n        rejectedToday: 3,\n        averageProcessingDays: 10,\n        totalUsers: 150\n    }\n};\n// Helper function to get user by ID\nconst getUserById = (id)=>{\n    return mockUsers.find((user)=>user.id === id);\n};\n// Helper function to get user by email\nconst getUserByEmail = (email)=>{\n    return mockUsers.find((user)=>user.email === email);\n};\n// Helper function to get requests by user ID\nconst getRequestsByUserId = (userId)=>{\n    return mockAssistanceRequests.filter((request)=>request.userId === userId);\n};\n// Helper function to get tasks by user ID\nconst getTasksByUserId = (userId)=>{\n    const user = getUserById(userId);\n    if (!user) return [];\n    return mockTasks[user.role] || [];\n};\n// Zakat Category Labels\nconst zakatCategoryLabels = {\n    fuqara: {\n        ar: \"الفقراء\",\n        en: \"The Poor\"\n    },\n    masakin: {\n        ar: \"المساكين\",\n        en: \"The Needy\"\n    },\n    amilin: {\n        ar: \"العاملين عليها\",\n        en: \"Zakat Administrators\"\n    },\n    muallafah: {\n        ar: \"المؤلفة قلوبهم\",\n        en: \"Those whose hearts are reconciled\"\n    },\n    riqab: {\n        ar: \"في الرقاب\",\n        en: \"To free slaves/captives\"\n    },\n    gharimin: {\n        ar: \"الغارمين\",\n        en: \"Those in debt\"\n    },\n    fisabilillah: {\n        ar: \"في سبيل الله\",\n        en: \"In the cause of Allah\"\n    },\n    ibnus_sabil: {\n        ar: \"ابن السبيل\",\n        en: \"The wayfarer/traveler\"\n    }\n};\n// Mock Beneficiaries Data\nconst mockBeneficiaries = [\n    {\n        id: \"ben-001\",\n        fullNameAr: \"أحمد محمد العبدالله\",\n        fullNameEn: \"Ahmed Mohammed Al-Abdullah\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1985-03-15\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966501234567\",\n        email: \"<EMAIL>\",\n        address: \"حي النهضة، شارع الملك فهد\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        postalCode: \"12345\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 85,\n        monthlyIncome: 2500,\n        familySize: 5,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-15\"),\n        lastVerificationDate: new Date(\"2024-01-20\"),\n        nextReviewDate: new Date(\"2024-07-15\"),\n        caseId: \"case-001\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 15000,\n        lastDistributionDate: new Date(\"2024-01-25\"),\n        distributionCount: 3,\n        documents: [\n            {\n                id: \"doc-001\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-01-15\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-16\"),\n                fileSize: 1024000,\n                mimeType: \"image/jpeg\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-25\"),\n        notes: \"Family breadwinner, needs regular support\"\n    },\n    {\n        id: \"ben-002\",\n        fullNameAr: \"فاطمة علي الزهراء\",\n        fullNameEn: \"Fatima Ali Al-Zahra\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1990-07-22\"),\n        gender: \"female\",\n        maritalStatus: \"widowed\",\n        phoneNumber: \"+966502345678\",\n        email: \"<EMAIL>\",\n        address: \"حي الملز، شارع العليا\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 92,\n        monthlyIncome: 1200,\n        familySize: 4,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-02-01\"),\n        lastVerificationDate: new Date(\"2024-02-05\"),\n        nextReviewDate: new Date(\"2024-08-01\"),\n        caseId: \"case-002\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 22000,\n        lastDistributionDate: new Date(\"2024-02-10\"),\n        distributionCount: 4,\n        familyMembers: [\n            {\n                id: \"fam-001\",\n                name: \"محمد علي الزهراء\",\n                relationship: \"son\",\n                age: 12,\n                isDependent: true,\n                hasSpecialNeeds: false\n            },\n            {\n                id: \"fam-002\",\n                name: \"عائشة علي الزهراء\",\n                relationship: \"daughter\",\n                age: 8,\n                isDependent: true,\n                hasSpecialNeeds: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-002\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-02-01\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-02-02\"),\n                fileSize: 2048000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-02-01\"),\n        updatedAt: new Date(\"2024-02-10\"),\n        notes: \"Widow with special needs child, priority case\"\n    },\n    {\n        id: \"ben-003\",\n        fullNameAr: \"خالد سعد الغامدي\",\n        fullNameEn: \"Khalid Saad Al-Ghamdi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1978-11-10\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966503456789\",\n        address: \"حي الشفا، طريق الملك عبدالعزيز\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 78,\n        monthlyIncome: 3200,\n        familySize: 6,\n        dependents: 4,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-15\"),\n        caseId: \"case-003\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 8000,\n        lastDistributionDate: new Date(\"2024-01-10\"),\n        distributionCount: 2,\n        documents: [\n            {\n                id: \"doc-003\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2024-02-15\"),\n                verified: false,\n                fileSize: 1536000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2024-02-15\"),\n        updatedAt: new Date(\"2024-02-16\"),\n        notes: \"Business owner facing financial difficulties\"\n    },\n    {\n        id: \"ben-004\",\n        fullNameAr: \"مريم عبدالرحمن القحطاني\",\n        fullNameEn: \"Maryam Abdulrahman Al-Qahtani\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1995-04-18\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966504567890\",\n        address: \"حي الروضة، شارع التحلية\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"ibnus_sabil\"\n        ],\n        primaryCategory: \"ibnus_sabil\",\n        eligibilityScore: 65,\n        monthlyIncome: 0,\n        familySize: 1,\n        dependents: 0,\n        status: \"pending_verification\",\n        verificationStatus: \"pending\",\n        registrationDate: new Date(\"2024-02-20\"),\n        priority: \"low\",\n        totalReceived: 0,\n        distributionCount: 0,\n        documents: [\n            {\n                id: \"doc-004\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-02-20\"),\n                verified: false,\n                fileSize: 896000,\n                mimeType: \"image/png\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-20\"),\n        updatedAt: new Date(\"2024-02-20\"),\n        notes: \"Student seeking temporary assistance\"\n    },\n    {\n        id: \"ben-005\",\n        fullNameAr: \"عبدالله يوسف الشهري\",\n        fullNameEn: \"Abdullah Yusuf Al-Shehri\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1982-09-05\"),\n        gender: \"male\",\n        maritalStatus: \"divorced\",\n        phoneNumber: \"+966505678901\",\n        address: \"حي العزيزية، شارع الأمير سلطان\",\n        city: \"الطائف\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 88,\n        monthlyIncome: 1800,\n        familySize: 3,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-05\"),\n        lastVerificationDate: new Date(\"2024-01-10\"),\n        nextReviewDate: new Date(\"2024-07-05\"),\n        caseId: \"case-005\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 18500,\n        lastDistributionDate: new Date(\"2024-02-01\"),\n        distributionCount: 5,\n        familyMembers: [\n            {\n                id: \"fam-003\",\n                name: \"سارة عبدالله الشهري\",\n                relationship: \"daughter\",\n                age: 10,\n                isDependent: true,\n                hasSpecialNeeds: false\n            },\n            {\n                id: \"fam-004\",\n                name: \"عمر عبدالله الشهري\",\n                relationship: \"son\",\n                age: 7,\n                isDependent: true,\n                hasSpecialNeeds: false\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-005\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-05\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-06\"),\n                fileSize: 1792000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-05\"),\n        updatedAt: new Date(\"2024-02-01\"),\n        notes: \"Divorced father with custody of children\"\n    },\n    {\n        id: \"ben-006\",\n        fullNameAr: \"نورا أحمد الحربي\",\n        fullNameEn: \"Nora Ahmed Al-Harbi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1988-12-03\"),\n        gender: \"female\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966506789012\",\n        email: \"<EMAIL>\",\n        address: \"حي الملقا، شارع الأمير محمد بن عبدالعزيز\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 91,\n        monthlyIncome: 1500,\n        familySize: 7,\n        dependents: 5,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-20\"),\n        lastVerificationDate: new Date(\"2024-01-25\"),\n        nextReviewDate: new Date(\"2024-07-20\"),\n        caseId: \"case-006\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 25000,\n        lastDistributionDate: new Date(\"2024-02-15\"),\n        distributionCount: 6,\n        documents: [\n            {\n                id: \"doc-006\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-20\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-01-21\"),\n                fileSize: 2304000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-01-20\"),\n        updatedAt: new Date(\"2024-02-15\"),\n        notes: \"Large family with multiple dependents\"\n    },\n    {\n        id: \"ben-007\",\n        fullNameAr: \"محمد عبدالله الدوسري\",\n        fullNameEn: \"Mohammed Abdullah Al-Dosari\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1975-06-14\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966507890123\",\n        address: \"حي الفيصلية، طريق الملك فهد\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 82,\n        monthlyIncome: 2800,\n        familySize: 4,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2023-12-10\"),\n        lastVerificationDate: new Date(\"2023-12-15\"),\n        nextReviewDate: new Date(\"2024-06-10\"),\n        caseId: \"case-007\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 12000,\n        lastDistributionDate: new Date(\"2024-01-30\"),\n        distributionCount: 4,\n        documents: [\n            {\n                id: \"doc-007\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2023-12-10\"),\n                verified: true,\n                verifiedBy: \"6\",\n                verifiedAt: new Date(\"2023-12-11\"),\n                fileSize: 1280000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2023-12-10\"),\n        updatedAt: new Date(\"2024-01-30\"),\n        notes: \"Small business owner with debt issues\"\n    },\n    {\n        id: \"ben-008\",\n        fullNameAr: \"عائشة سالم القرني\",\n        fullNameEn: \"Aisha Salem Al-Qarni\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1992-08-27\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966508901234\",\n        address: \"حي الشرفية، شارع الستين\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 75,\n        monthlyIncome: 1000,\n        familySize: 2,\n        dependents: 1,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-25\"),\n        caseId: \"case-008\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 3000,\n        lastDistributionDate: new Date(\"2024-01-15\"),\n        distributionCount: 1,\n        familyMembers: [\n            {\n                id: \"fam-005\",\n                name: \"فاطمة سالم القرني\",\n                relationship: \"mother\",\n                age: 65,\n                isDependent: true,\n                hasSpecialNeeds: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-008\",\n                type: \"medical_report\",\n                name: \"Medical Report for Mother\",\n                uploadDate: new Date(\"2024-02-25\"),\n                verified: false,\n                fileSize: 3072000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-25\"),\n        updatedAt: new Date(\"2024-02-26\"),\n        notes: \"Caring for elderly mother with medical needs\"\n    }\n];\n// Helper functions for beneficiaries\nconst getBeneficiaryById = (id)=>{\n    return mockBeneficiaries.find((beneficiary)=>beneficiary.id === id);\n};\nconst getBeneficiariesByStatus = (status)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.status === status);\n};\nconst getBeneficiariesByCategory = (category)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.zakatCategories.includes(category) || beneficiary.primaryCategory === category);\n};\nconst searchBeneficiaries = (searchTerm)=>{\n    const term = searchTerm.toLowerCase();\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.fullNameAr.toLowerCase().includes(term) || beneficiary.fullNameEn.toLowerCase().includes(term) || beneficiary.nationalId.includes(term) || beneficiary.phoneNumber.includes(term) || beneficiary.email?.toLowerCase().includes(term));\n};\nconst getBeneficiaryStats = ()=>{\n    const total = mockBeneficiaries.length;\n    const approved = mockBeneficiaries.filter((b)=>b.status === \"approved\").length;\n    const pending = mockBeneficiaries.filter((b)=>b.status === \"pending_verification\").length;\n    const underReview = mockBeneficiaries.filter((b)=>b.status === \"under_review\").length;\n    const totalDistributed = mockBeneficiaries.reduce((sum, b)=>sum + b.totalReceived, 0);\n    return {\n        total,\n        approved,\n        pending,\n        underReview,\n        totalDistributed,\n        averageDistribution: total > 0 ? Math.round(totalDistributed / total) : 0\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/mock-data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/preact","vendor-chunks/oidc-token-hash","vendor-chunks/object-hash","vendor-chunks/lru-cache"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();