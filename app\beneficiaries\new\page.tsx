'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, UserPlus } from 'lucide-react'

export default function NewBeneficiaryPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()

  if (!session?.user) {
    return null
  }

  // Check if user has access to beneficiary management
  const hasAccess = ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'].includes(session.user.role)
  
  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
            <p className="text-muted-foreground">{t('no_registration_access')}</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/beneficiaries">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('back')}
            </Link>
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">
              {t('beneficiary_registration')}
            </h1>
            <p className="text-muted-foreground">
              {t('beneficiary_registration_desc')}
            </p>
          </div>
        </div>

        {/* Coming Soon Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <UserPlus className="h-8 w-8 text-primary" />
            </div>
            <CardTitle className="text-2xl">{t('registration_form_coming')}</CardTitle>
            <CardDescription className="text-lg">
              {t('registration_form_desc')}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4">
              <p className="text-muted-foreground">
                {t('will_include_features')}
              </p>
              <div className="grid gap-2 text-sm text-muted-foreground max-w-md mx-auto">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('multi_step_form')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('dual_language_input')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('zakat_categories_selection')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('document_upload')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('data_validation')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{t('duplicate_detection')}</span>
                </div>
              </div>
              <Button asChild className="mt-6">
                <Link href="/beneficiaries">
                  {t('back_to_beneficiaries')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
